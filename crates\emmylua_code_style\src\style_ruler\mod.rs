mod basic_space;

use crate::{format::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, styles::LuaCodeStyle};

#[allow(unused)]
pub fn apply_styles(formatter: &mut <PERSON>aFormatter, styles: &LuaCodeStyle) {
    apply_style::<basic_space::BasicSpaceRuler>(formatter, styles);
}

pub trait StyleRuler {
    /// Apply the style rules to the formatter
    fn apply_style(formatter: &mut <PERSON>aFormatter, styles: &LuaCodeStyle);
}

pub fn apply_style<T: StyleRuler>(formatter: &mut <PERSON>aFormatter, styles: &LuaCodeStyle) {
    T::apply_style(formatter, styles)
}
