name: Bump Version & Upload
on:
  push:
    tags:
      - '*'

jobs:
  bump:
    name: Bump version
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: <PERSON>lone repo
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable

      - id: version
        name: Bump version
        run: |
          echo "current ref ${{ github.ref }}"
          cargo run -p edit_version -- ${{ github.ref }}
          echo "VERSION=$(sed 's:refs/tags/::' <<< ${{ github.ref }})" >> "$GITHUB_OUTPUT"

      - name: Push changes
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: Bump version to ${{ steps.version.outputs.VERSION }}
          file_pattern: 'crates/*/Cargo.toml'
          
  upload-crates:
    needs: bump
    uses: ./.github/workflows/upload_crates.yml

  upload-cachix:
    needs: bump
    uses: ./.github/workflows/upload_cachix.yml
