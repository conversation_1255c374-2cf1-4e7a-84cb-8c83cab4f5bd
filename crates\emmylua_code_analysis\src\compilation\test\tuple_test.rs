#[cfg(test)]
mod tests {
    use crate::{DiagnosticCode, VirtualWorkspace};

    #[test]
    fn test_issue_231() {
        let mut ws = VirtualWorkspace::new_with_init_std_lib();
        assert!(ws.check_code_for(
            DiagnosticCode::AssignTypeMismatch,
            r#"

            --- @type [boolean, string]
            local ret = { coroutine.resume(coroutine.create(function () end), ...) }
            "#
        ));
    }

    #[test]
    fn test_union_tuple() {
        let mut ws = VirtualWorkspace::new();
        ws.def(
            r#"
                local Pos = {
                    [1] = {
                        { 36,  777 },
                    },
                    [2] = {
                        { 826, 244 },
                    },
                }
                ---@type int
                local cur
                ---@type int
                local index

                local points = Pos[cur] 
                ---@cast points -?
                local point = points[index] ---@cast point -?
                A = point[1]

            "#,
        );
        let ty = ws.expr_ty("A");
        assert_eq!(ws.humanize_type(ty), "(36|826)");
    }
}
