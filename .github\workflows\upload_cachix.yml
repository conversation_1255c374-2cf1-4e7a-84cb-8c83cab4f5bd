name: "Upload to <PERSON>achi<PERSON>"

on:
  workflow_dispatch:
  workflow_call:
  push:
    tags:
      - "*"

jobs:
  build-and-cache:
    strategy:
      fail-fast: true
      matrix:
        nixChannel:
          - nixos-25.05
          - nixos-24.11 # remove this when 25.05 is out of beta
          - nixos-unstable
          - nixpkgs-unstable
          
    runs-on: ubuntu-latest
    steps:
    - name: <PERSON><PERSON> repo
      uses: actions/checkout@v4
      
    - name: <PERSON>up Nix
      uses: cachix/install-nix-action@v30
      with:
        nix_path: nixpkgs=https://github.com/NixOS/nixpkgs/archive/refs/heads/${{ matrix.nixChannel }}.tar.gz
        extra_nix_config: |
          experimental-features = nix-command flakes
          access-tokens = github.com=${{ secrets.RELEASE }}

    - name: Show the current nixpkgs version
      run: |
        nix eval --impure --raw --expr '"Building for nixpkgs " + (import <nixpkgs> { }).lib.version'
    
    - name: Setup Cachix
      uses: cachix/cachix-action@v15
      with:
        name: emmylua-analyzer
        authToken: '${{ secrets.CACHIX }}'
        
    - name: Build emmylua-analyzer
      run: |
        for x in emmylua_{ls,doc_cli,check}; do
          nix build --impure --expr \
            "with import <nixpkgs> {}; callPackage (import ./nix/packages.nix).$x {}"
        done
