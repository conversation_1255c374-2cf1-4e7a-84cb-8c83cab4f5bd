name: Test

on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - main

env:
  RUST_BACKTRACE: 1
  CARGO_TERM_COLOR: always
  CLICOLOR: 1

jobs:
  spelling:
    name: Spell Check with Typos
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Spell Check Repo
      uses: crate-ci/typos@master

  reformat:
    name: Rustfmt
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Install Rust toolchain
      uses: dtolnay/rust-toolchain@stable
    - name: Run Rustfmt
      run: cargo fmt --all -- --check

  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
      - name: Run Tests
        run: cargo test

  check-schema:
    name: Check schema generation
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
        id: toolchain
      - name: Check schema
        run: |
          # Generate schema
          cargo run --bin schema_json_gen
          # Check for uncommitted changes
          if [[ -n "$(git status --porcelain)" ]]; then
            echo '❌ Uncommitted changes detected after running `cargo run --bin schema_json_gen`:'
            git --no-pager diff
            exit 1
          else
            echo "✅ No changes — schema is up to date."
          fi
