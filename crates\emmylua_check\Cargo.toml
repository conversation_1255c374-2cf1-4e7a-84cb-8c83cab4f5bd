[package]
name = "emmylua_check"
version = "0.8.1"
edition = "2021"
authors = ["CppCXY"]
description = "A command-line tool for checking lua code."
license = "MIT"
repository = "https://github.com/CppCXY/emmylua-analyzer-rust"
readme = "README.md"
keywords = ["emmylua", "doc", "lua", "cli"]
categories = ["development-tools"]

[dependencies]
# local
emmylua_code_analysis.workspace = true
emmylua_parser.workspace = true

# external
serde.workspace = true
serde_json.workspace = true
lsp-types.workspace = true
log.workspace = true
fern.workspace = true
rowan.workspace = true
walkdir.workspace = true
tokio.workspace = true
tokio-util.workspace = true
ariadne.workspace = true
clap.workspace = true
