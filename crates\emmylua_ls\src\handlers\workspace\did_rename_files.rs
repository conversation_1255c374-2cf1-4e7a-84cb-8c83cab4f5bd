use std::{
    collections::HashMap,
    path::{Path, PathBuf},
    str::FromStr,
};

use emmylua_code_analysis::{
    file_path_to_uri, uri_to_file_path, FileId, LuaCompilation, LuaType, SemanticModel,
};
use emmylua_parser::{LuaAstNode, LuaCallExpr, LuaIndexExpr};
use lsp_types::{
    FileRename, MessageActionItem, MessageType, RenameFilesParams, ShowMessageRequestParams, Uri,
};
use tokio_util::sync::CancellationToken;
use walkdir::WalkDir;

use crate::{context::ServerContextSnapshot, handlers::ClientConfig};

pub async fn on_did_rename_files_handler(
    context: ServerContextSnapshot,
    params: RenameFilesParams,
) -> Option<()> {
    dbg!(&params);
    let analysis = context.analysis.write().await;
    let workspace_manager = context.workspace_manager.read().await;
    let mut all_renames = vec![];

    for file_rename in params.files {
        let FileRename { old_uri, new_uri } = file_rename;
        let mut renames = vec![];

        let old_uri = Uri::from_str(&old_uri).ok()?;
        let new_uri = Uri::from_str(&new_uri).ok()?;

        let old_path = uri_to_file_path(&old_uri)?;
        let new_path = uri_to_file_path(&new_uri)?;

        // 检查是否是Lua文件
        if is_lua_file(&old_path, &workspace_manager.client_config)
            && is_lua_file(&new_path, &workspace_manager.client_config)
        {
            renames.push((old_uri.clone(), new_uri.clone()));
        } else {
            // 有可能是目录重命名, 需要收集目录下所有 lua 文件
            if let Some(collected_renames) =
                collect_directory_lua_files(&old_path, &new_path, &workspace_manager.client_config)
            {
                renames.extend(collected_renames);
            }
        }

        all_renames.extend(renames);
    }

    // 如果有重命名的文件, 弹窗询问用户是否要修改require路径
    if !all_renames.is_empty() {
        try_modify_require_path(&analysis.compilation, &all_renames);
        drop(analysis);

        let client = context.client.clone();
        drop(workspace_manager);

        let show_message_params = ShowMessageRequestParams {
            typ: MessageType::INFO,
            message: t!("Do you want to modify the require path?").to_string(),
            actions: Some(vec![MessageActionItem {
                title: t!("Modify").to_string(),
                properties: HashMap::new(),
            }]),
        };

        // 发送弹窗请求
        let cancel_token = CancellationToken::new();
        if let Some(selected_action) = client
            .show_message_request(show_message_params, cancel_token)
            .await
        {
            dbg!(&selected_action);
            if selected_action.title == t!("Modify") {
                dbg!(&all_renames);
            }
        }
    }

    Some(())
}

/// 收集目录重命名后所有的Lua文件
fn collect_directory_lua_files(
    old_path: &PathBuf,
    new_path: &PathBuf,
    client_config: &ClientConfig,
) -> Option<Vec<(Uri, Uri)>> {
    // 检查新路径是否是目录（旧路径已经不存在了）
    if !new_path.is_dir() {
        return None;
    }

    let mut renames = vec![];

    // 遍历新目录下的所有Lua文件
    for entry in WalkDir::new(new_path)
        .into_iter()
        .filter_map(|e| e.ok())
        .filter(|e| e.file_type().is_file())
    {
        let new_file_path = entry.path();

        // 检查是否是Lua文件
        if is_lua_file(new_file_path, client_config) {
            // 计算在新目录中的相对路径
            if let Ok(relative_path) = new_file_path.strip_prefix(new_path) {
                // 根据目录重命名推算出对应的旧文件路径
                let old_file_path = old_path.join(relative_path);

                // 转换为URI
                if let (Some(old_file_uri), Some(new_file_uri)) = (
                    file_path_to_uri(&old_file_path),
                    file_path_to_uri(&new_file_path.to_path_buf()),
                ) {
                    renames.push((old_file_uri, new_file_uri));
                }
            }
        }
    }

    if renames.is_empty() {
        None
    } else {
        Some(renames)
    }
}

/// 检查文件路径是否是Lua文件
fn is_lua_file(file_path: &Path, client_config: &ClientConfig) -> bool {
    let file_name = file_path.to_string_lossy();

    if file_name.ends_with(".lua") {
        return true;
    }

    // 检查客户端配置的扩展名
    for extension in &client_config.extensions {
        if file_name.ends_with(extension) {
            return true;
        }
    }

    false
}

fn try_modify_require_path(compilation: &LuaCompilation, renames: &Vec<(Uri, Uri)>) -> Option<()> {
    for file_id in compilation.get_db().get_vfs().get_all_file_ids() {
        if let Some(semantic_model) = compilation.get_semantic_model(file_id) {
            for call_expr in semantic_model.get_root().descendants::<LuaCallExpr>() {
                if call_expr.is_require() {
                    check_convert(&semantic_model, call_expr, renames, file_id);
                }
            }
        }
    }
    None
}

fn check_convert(
    semantic_model: &SemanticModel,
    call_expr: LuaCallExpr,
    renames: &Vec<(Uri, Uri)>,
    current_file_id: FileId, // 当前文件id
) -> Option<()> {
    if let Some(_) = call_expr.get_parent::<LuaIndexExpr>() {
        return Some(());
    }

    let args_list = call_expr.get_args_list()?;
    let arg_expr = args_list.get_args().next()?;
    let ty = semantic_model.infer_expr(arg_expr).unwrap_or(LuaType::Any);
    let name = if let LuaType::StringConst(s) = ty {
        s
    } else {
        return Some(());
    };
    let current_uri = file_path_to_uri(
        semantic_model
            .get_db()
            .get_vfs()
            .get_file_path(&current_file_id)?,
    )?;

    for (old_uri, new_uri) in renames {
        if is_matched_uri(semantic_model, &current_uri, old_uri, name.as_str()).unwrap_or(false) {
            // TODO: Handle the matched URI case - should modify the require path
            dbg!("Matched URI:", old_uri, new_uri);
        }
    }

    Some(())
}

fn is_matched_uri(
    semantic_model: &SemanticModel,
    source_uri: &Uri,
    target_uri: &Uri,
    name: &str,
) -> Option<bool> {
    let emmyrc = semantic_model.get_emmyrc();
    let searchers = &emmyrc.runtime.require_pattern;
    let strict = emmyrc.strict.require_path;
    let separator = &emmyrc.completion.auto_require_separator;

    // 标准化路径：将分隔符替换为 '/'
    let normalized_path = name.replace(separator, "/");

    for searcher in searchers {
        // 构建文件路径：替换 '?' 为路径，并标准化
        let file_path = searcher
            .replace('?', &normalized_path.replace('%', "%%"))
            .replace('\\', "/");

        // 构建尾部路径用于匹配
        let tail = format!(
            "/{}",
            file_path
                .trim_start_matches("file:")
                .trim_start_matches('/')
        );

        if target_uri.as_str().ends_with(&tail) {
            // 获取父级 URI，默认使用目标 URI
            let parent_uri = get_parent_uri(semantic_model, source_uri, target_uri)
                .filter(|uri| !uri.is_empty())
                .unwrap_or_else(|| "file:///".to_string());

            // 计算相对路径
            let target_str = target_uri.as_str();
            if let Some(relative_part) = target_str.strip_prefix(&parent_uri) {
                if let Some(relative) = relative_part.strip_suffix(&tail) {
                    // 检查严格模式
                    if !strict || matches!(relative, "" | "/") {
                        return Some(true);
                    }
                }
            }
        }
    }

    Some(false)
}

/// 获取父级URI, 根据workspace配置查找匹配的根目录
fn get_parent_uri(
    semantic_model: &SemanticModel,
    source_uri: &Uri,
    _target_uri: &Uri,
) -> Option<String> {
    let source_path = uri_to_file_path(source_uri)?;
    let workspace_roots = &semantic_model.get_emmyrc().workspace.workspace_roots;

    workspace_roots.iter().find_map(|root| {
        let workspace_path = Path::new(root);
        if source_path.starts_with(workspace_path) {
            file_path_to_uri(&workspace_path.to_path_buf()).map(|uri| uri.to_string())
        } else {
            None
        }
    })
}
