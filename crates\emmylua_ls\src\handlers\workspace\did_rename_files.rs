use std::{
    collections::HashMap,
    path::{Path, PathBuf},
    str::FromStr,
};

use emmylua_code_analysis::{
    file_path_to_uri, uri_to_file_path, FileId, LuaCompilation, LuaType, SemanticModel,
};
use emmylua_parser::{LuaAstNode, LuaCallExpr, LuaIndexExpr};
use lsp_types::{
    FileRename, MessageActionItem, MessageType, RenameFilesParams, ShowMessageRequestParams, Uri,
};
use tokio_util::sync::CancellationToken;
use walkdir::WalkDir;

use crate::{context::ServerContextSnapshot, handlers::ClientConfig};

pub async fn on_did_rename_files_handler(
    context: ServerContextSnapshot,
    params: RenameFilesParams,
) -> Option<()> {
    dbg!(&params);
    let analysis = context.analysis.write().await;
    let workspace_manager = context.workspace_manager.read().await;
    let mut all_renames = vec![];

    for file_rename in params.files {
        let FileRename { old_uri, new_uri } = file_rename;
        let mut renames = vec![];

        let old_uri = Uri::from_str(&old_uri).ok()?;
        let new_uri = Uri::from_str(&new_uri).ok()?;

        let old_path = uri_to_file_path(&old_uri)?;
        let new_path = uri_to_file_path(&new_uri)?;

        // 检查是否是Lua文件
        if is_lua_file(&old_path, &workspace_manager.client_config)
            && is_lua_file(&new_path, &workspace_manager.client_config)
        {
            renames.push((old_uri.clone(), new_uri.clone()));
        } else {
            // 有可能是目录重命名, 需要收集目录下所有 lua 文件
            if let Some(collected_renames) =
                collect_directory_lua_files(&old_path, &new_path, &workspace_manager.client_config)
            {
                renames.extend(collected_renames);
            }
        }

        all_renames.extend(renames);
    }

    // 如果有重命名的文件, 弹窗询问用户是否要修改require路径
    if !all_renames.is_empty() {
        try_modify_require_path(&analysis.compilation, &all_renames);
        drop(analysis);

        let client = context.client.clone();
        drop(workspace_manager);

        let show_message_params = ShowMessageRequestParams {
            typ: MessageType::INFO,
            message: t!("Do you want to modify the require path?").to_string(),
            actions: Some(vec![MessageActionItem {
                title: t!("Modify").to_string(),
                properties: HashMap::new(),
            }]),
        };

        // 发送弹窗请求
        let cancel_token = CancellationToken::new();
        if let Some(selected_action) = client
            .show_message_request(show_message_params, cancel_token)
            .await
        {
            dbg!(&selected_action);
            if selected_action.title == t!("Modify") {
                dbg!(&all_renames);
            }
        }
    }

    Some(())
}

/// 收集目录重命名后所有的Lua文件
fn collect_directory_lua_files(
    old_path: &PathBuf,
    new_path: &PathBuf,
    client_config: &ClientConfig,
) -> Option<Vec<(Uri, Uri)>> {
    // 检查新路径是否是目录（旧路径已经不存在了）
    if !new_path.is_dir() {
        return None;
    }

    let mut renames = vec![];

    // 遍历新目录下的所有Lua文件
    for entry in WalkDir::new(new_path)
        .into_iter()
        .filter_map(|e| e.ok())
        .filter(|e| e.file_type().is_file())
    {
        let new_file_path = entry.path();

        // 检查是否是Lua文件
        if is_lua_file(new_file_path, client_config) {
            // 计算在新目录中的相对路径
            if let Ok(relative_path) = new_file_path.strip_prefix(new_path) {
                // 根据目录重命名推算出对应的旧文件路径
                let old_file_path = old_path.join(relative_path);

                // 转换为URI
                if let (Some(old_file_uri), Some(new_file_uri)) = (
                    file_path_to_uri(&old_file_path),
                    file_path_to_uri(&new_file_path.to_path_buf()),
                ) {
                    renames.push((old_file_uri, new_file_uri));
                }
            }
        }
    }

    if renames.is_empty() {
        None
    } else {
        Some(renames)
    }
}

/// 检查文件路径是否是Lua文件
fn is_lua_file(file_path: &Path, client_config: &ClientConfig) -> bool {
    let file_name = file_path.to_string_lossy();

    if file_name.ends_with(".lua") {
        return true;
    }

    // 检查客户端配置的扩展名
    for extension in &client_config.extensions {
        if file_name.ends_with(extension) {
            return true;
        }
    }

    false
}

fn try_modify_require_path(compilation: &LuaCompilation, renames: &Vec<(Uri, Uri)>) -> Option<()> {
    for file_id in compilation.get_db().get_vfs().get_all_file_ids() {
        if let Some(semantic_model) = compilation.get_semantic_model(file_id) {
            for call_expr in semantic_model.get_root().descendants::<LuaCallExpr>() {
                if call_expr.is_require() {
                    check_convert(&semantic_model, call_expr, renames, file_id);
                }
            }
        }
    }
    None
}

fn check_convert(
    semantic_model: &SemanticModel,
    call_expr: LuaCallExpr,
    renames: &Vec<(Uri, Uri)>,
    current_file_id: FileId, // 当前文件id
) -> Option<()> {
    if let Some(_) = call_expr.get_parent::<LuaIndexExpr>() {
        return Some(());
    }

    let args_list = call_expr.get_args_list()?;
    let arg_expr = args_list.get_args().next()?;
    let ty = semantic_model.infer_expr(arg_expr).unwrap_or(LuaType::Any);
    let name = if let LuaType::StringConst(s) = ty {
        s
    } else {
        return Some(());
    };
    let current_uri = file_path_to_uri(
        semantic_model
            .get_db()
            .get_vfs()
            .get_file_path(&current_file_id)?,
    )?;

    for (old_uri, new_uri) in renames {
        if is_matched_uri(semantic_model, &current_uri, old_uri, name.as_str()).unwrap_or(false) {
            // TODO: Handle the matched URI case - should modify the require path
            dbg!("Matched URI:", old_uri, new_uri);
        }
    }

    Some(())
}

fn is_matched_uri(
    semantic_model: &SemanticModel,
    source_uri: &Uri,
    target_uri: &Uri,
    name: &str,
) -> Option<bool> {
    let searchers = &semantic_model.get_emmyrc().runtime.require_pattern;
    let strict = semantic_model.get_emmyrc().strict.require_path;
    let separator = &semantic_model
        .get_emmyrc()
        .completion
        .auto_require_separator;

    // 将name中的separator替换为'/'得到path
    let path = name.replace(separator, "/");

    // 遍历searchers
    for searcher in searchers {
        // 将searcher中的'?'替换为path（需要转义%）
        let escaped_path = path.replace('%', "%%");
        let mut fspath = searcher.replace('?', &escaped_path);

        // 标准化文件路径（替换反斜杠为正斜杠）
        fspath = fspath.replace('\\', "/");

        // 构造tail路径，类似Lua中的逻辑
        // files.normalize(fspath) 在这里简化为路径标准化
        let normalized_fspath = normalize_path(&fspath);

        // 构造tail，类似 '/' .. furi.encode(fspath):gsub('^file:[/]*', '')
        let tail = format!(
            "/{}",
            normalized_fspath
                .trim_start_matches("file:")
                .trim_start_matches('/')
        );

        // util.stringEndWith(uri, tail) - 检查target_uri是否以tail结尾
        if target_uri.as_str().ends_with(&tail) {
            // 获取parent_uri（类似files.getLibraryUri(suri, uri) or uri）
            let parent_uri = get_parent_uri(semantic_model, source_uri, target_uri)
                .unwrap_or_else(|| target_uri.to_string());

            // 确保parent_uri不为空
            let parent_uri = if parent_uri.is_empty() {
                "file:///".to_string()
            } else {
                parent_uri
            };

            // 计算相对路径：uri:sub(#parentUri + 1):sub(1, - #tail)
            let target_str = target_uri.as_str();
            let parent_len = parent_uri.len();

            if target_str.len() > parent_len {
                let after_parent = &target_str[parent_len..];
                let relative = if after_parent.len() >= tail.len() {
                    &after_parent[..after_parent.len() - tail.len()]
                } else {
                    after_parent
                };

                // 检查strict模式：not strict or relative == '/' or relative == ''
                if !strict || relative == "/" || relative.is_empty() {
                    return Some(true);
                }
            }
        }
    }

    Some(false)
}

/// 路径标准化函数，类似Lua中的files.normalize
fn normalize_path(path: &str) -> String {
    // 这里简化实现，主要是标准化路径分隔符
    path.replace('\\', "/")
}

/// 获取父级URI，这里简化实现
fn get_parent_uri(
    semantic_model: &SemanticModel,
    source_uri: &Uri,
    _target_uri: &Uri,
) -> Option<String> {
    // 这里简化处理，应该根据workspace配置获取library URI
    // 如果没有配置library，就使用根目录
    let emmyrc = semantic_model.get_emmyrc();

    // 尝试从workspace roots中找到匹配的
    if let Some(source_path) = uri_to_file_path(source_uri) {
        for workspace_root in &emmyrc.workspace.workspace_roots {
            let workspace_path = std::path::Path::new(workspace_root);
            if source_path.starts_with(workspace_path) {
                if let Some(workspace_uri) = file_path_to_uri(&workspace_path.to_path_buf()) {
                    return Some(workspace_uri.to_string());
                }
            }
        }
    }

    // 如果没找到匹配的workspace，返回None（让调用者处理）
    None
}
