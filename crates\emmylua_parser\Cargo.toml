[package]
name = "emmylua_parser"
version = "0.11.0"
edition = "2021"
authors = ["CppCXY"]
description = "A parser for EmmyLua and luals"
license = "MIT"
repository = "https://github.com/CppCXY/emmylua-analyzer-rust"
readme = "README.md"
keywords = ["emmylua", "luals", "parser", "lua"]
categories = ["development-tools", "parsing"]

[dependencies]
rowan.workspace = true
rust-i18n.workspace = true
serde.workspace = true

[package.metadata.i18n]
available-locales = ["en", "zh_CN", "zh_HK", "zh_TW"]
default-locale = "en"