std.string: |
  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string"])

std.string.byte: |
  返回字符 `s[i]`， `s[i+1]`， ...　，`s[j]` 的内部数字编码。

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.byte"])

std.string.char: |
  接收零或更多的整数。 返回和参数数量相同长度的字符串。 其中每个字符的内部编码值等于对应的参数值。

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.char"])

std.string.dump: |
  返回包含有以二进制方式表示的（一个 *二进制代码块* ）指定函数的字符串。

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.dump"])

std.string.find: |
  查找第一个字符串中匹配到的 `pattern`（参见 [§6.4.1](command:extension.lua.doc?["en-us/54/manual.html/6.4.1"])）。

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.find"])

std.string.format: |
  返回不定数量参数的格式化版本，格式化串为第一个参数。

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.format"])

std.string.gmatch: |
  返回一个迭代器函数。 每次调用这个函数都会继续以 `pattern` （参见 [§6.4.1](command:extension.lua.doc?["en-us/54/manual.html/6.4.1"])） 对 s 做匹配，并返回所有捕获到的值。

  下面这个例子会循环迭代字符串 s 中所有的单词， 并逐行打印：
  ```lua
      s =
  "hello world from Lua"
      for w in string.gmatch(s, "%a+") do
          print(w)
      end
  ```

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.gmatch"])

std.string.gsub: |
  将字符串 s 中，所有的（或是在 n 给出时的前 n 个） pattern （参见 [§6.4.1](command:extension.lua.doc?["en-us/54/manual.html/6.4.1"])）都替换成 repl ，并返回其副本。

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.gsub"])

std.string.len: |
  返回其长度。

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.len"])

std.string.lower: |
  将其中的大写字符都转为小写后返回其副本。

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.lower"])

std.string.match: |
  在字符串 s 中找到第一个能用 pattern （参见 [§6.4.1](command:extension.lua.doc?["en-us/54/manual.html/6.4.1"])）匹配到的部分。 如果能找到，match 返回其中的捕获物； 否则返回 nil 。

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.match"])

std.string.pack: |
  返回一个打包了（即以二进制形式序列化） v1, v2 等值的二进制字符串。 字符串 fmt 为打包格式（参见 [§6.4.2](command:extension.lua.doc?["en-us/54/manual.html/6.4.2"])）。

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.pack"])

std.string.packsize: |
  返回以指定格式用 [string.pack](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.pack"]) 打包的字符串的长度。 格式化字符串中不可以有变长选项 's' 或 'z' （参见 [§6.4.2](command:extension.lua.doc?["en-us/54/manual.html/6.4.2"])）。

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.packsize"])

std.string.rep: |
  返回 `n` 个字符串 `s` 以字符串 `sep` 为分割符连在一起的字符串。 默认的 `sep` 值为空字符串（即没有分割符）。 如果 `n` 不是正数则返回空串。

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.rep"])

std.string.reverse: |
  返回字符串 s 的翻转串。

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.reverse"])

std.string.sub: |
  返回字符串的子串， 该子串从 `i` 开始到 `j` 为止。

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.sub"])

std.string.unpack: |
  返回以格式 fmt （参见 [§6.4.2](command:extension.lua.doc?["en-us/54/manual.html/6.4.2"])） 打包在字符串 s （参见 string.pack） 中的值。

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.unpack"])

std.string.upper: |
  接收一个字符串，将其中的小写字符都转为大写后返回其副本。

  [查看文档](command:extension.lua.doc?["en-us/54/manual.html/pdf-string.upper"])