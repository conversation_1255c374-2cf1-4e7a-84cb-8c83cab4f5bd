tags.class: |
  The `class` tag is used to document a class or a struct.
  Example:
  ```lua
  ---@class MyClass
  local MyClass = {}
  ```
tags.enum: |
  The `enum` tag is used to document an enumeration.
  Example:
  ```lua
  ---@enum MyEnum
  local MyEnum = {
    Value1 = 1,
    Value2 = 2
  }
  ```
tags.interface: |
  The `interface` is deprecated, use `class` instead.
  Example:
  ```lua
  ---@interface MyInterface
  local MyInterface = {}
  ```
tags.alias: |
  The `alias` tag is used to document a type alias.
  Example:
  ```lua
  ---@alias MyTypeAlias string|number
  ```
tags.field: |
  The `field` tag is used to document a field of a class or a struct.
  Example:
  ```lua
  ---@class MyClass
  ---@field publicField string
  MyClass = {}
  ```
tags.type: |
  The `type` tag is used to document a type.
  Example:
  ```lua
  ---@type string
  local myString = "Hello"
  ```
tags.param: |
  The `param` tag is used to document a function parameter.
  Example:
  ```lua
  ---@param paramName string
  function myFunction(paramName)
  end
  ```
tags.return: |
  The `return` tag is used to document the return value of a function.
  Example:
  ```lua
  ---@return string
  function myFunction()
    return "Hello"
  end
  ```
tags.generic: |
  The `generic` tag is used to document generic types.
  Example:
  ```lua
  ---@generic T
  ---@param param T
  ---@return T
  function identity(param)
    return param
  end
  ```
tags.see: |
  The `see` tag is used to reference another documentation entry.
  Example:
  ```lua
  ---@see otherFunction
  function myFunction()
  end
  ```
tags.deprecated: |
  The `deprecated` tag is used to mark a function or a field as deprecated.
  Example:
  ```lua
  ---@deprecated
  function oldFunction()
  end
  ```
tags.cast: |
  The `cast` tag is used to document a type cast.
  Example:
  ```lua
  ---@cast varName string
  local varName = someValue
  ```
tags.overload: |
  The `overload` tag is used to document an overloaded function.
  Example:
  ```lua
  ---@overload fun(param: string):void
  function myFunction(param)
  end
  ```
tags.async: |
  The `async` tag is used to document an asynchronous function.
  Example:
  ```lua
  ---@async
  function asyncFunction()
  end
  ```
tags.public: |
  The `public` tag is used to mark a field or a function as public.
  Example:
  ```lua
  ---@public
  MyClass.publicField = ""
  ```
tags.protected: |
  The `protected` tag is used to mark a field or a function as protected.
  Example:
  ```lua
  ---@protected
  MyClass.protectedField = ""
  ```
tags.private: |
  The `private` tag is used to mark a field or a function as private.
  Example:
  ```lua
  ---@private
  local privateField = ""
  ```
tags.package: |
  The `package` tag is used to document a package.
  Example:
  ```lua
  ---@package
  local myPackage = {}
  ```
tags.meta: |
  The `meta` tag is used to document meta information.
  Example:
  ```lua
  ---@meta
  local metaInfo = {}
  ```
tags.diagnostic: |
  The `diagnostic` tag is used to document diagnostic information.
  Example:
  ```lua
  ---@diagnostic disable-next-line: unused-global
  local unusedVar = 1
  ```
tags.version: |
  The `version` tag is used to document the version of a module or a function.
  Example:
  ```lua
  ---@version 1.0
  function myFunction()
  end
  ```
tags.as: |
  The `as` tag is used to document type assertions.
  Example:
  ```lua
  ---@as string
  local varName = someValue
  ```
tags.nodiscard: |
  The `nodiscard` tag is used to indicate that the return value should not be discarded.
  Example:
  ```lua
  ---@nodiscard
  function importantFunction()
    return "Important"
  end
  ```
tags.operator: |
  The `operator` tag is used to document operator overloads.
  Example:
  ```lua
  ---@class 
  ---@operator add(MyClass):MyClass
  ```
tags.module: |
  The `module` tag is used to document a module.
  Example:
  ```lua
  ---@module MyModule
  local MyModule = {}
  ```
tags.namespace: |
  The `namespace` tag is used to document a namespace.
  Example:
  ```lua
  ---@namespace MyNamespace
  ```
tags.using: |
  The `using` tag is used to document using declarations.
  Example:
  ```lua
  ---@using MyNamespace
  ```
tags.source: |
  The `source` tag is used to document the source of a function or a module.
  Example:
  ```lua
  ---@source https://example.com/source
  function myFunction()
  end
  ```
tags.readonly: |
  The `readonly` tag is used to mark a field as read-only.
  but it is not supported in current
  Example:
  ```lua
  ---@readonly
  MyClass.readonlyField = "constant"
  ```
