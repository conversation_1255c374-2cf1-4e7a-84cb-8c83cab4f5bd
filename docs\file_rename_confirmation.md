# 文件重命名确认功能

## 概述

当用户在VSCode中重命名Lua文件或包含Lua文件的目录时，EmmyLua语言服务器会自动检测到这些变化，并弹出确认对话框询问用户是否要同时修改相关的`require`路径。

## 功能特性

1. **自动检测文件重命名**: 监听VSCode的文件重命名事件
2. **智能识别Lua文件**: 支持`.lua`文件以及用户配置的其他扩展名
3. **目录重命名支持**: 当重命名目录时，会递归检测目录下的所有Lua文件
4. **用户确认机制**: 弹出对话框让用户选择是否修改require路径

## 实现细节

### 核心组件

1. **ClientProxy.show_message_request()**: 发送弹窗请求到VSCode客户端
2. **on_did_rename_files_handler()**: 处理文件重命名事件的主要函数
3. **collect_directory_lua_files()**: 收集目录下所有Lua文件的重命名信息

### 弹窗界面

弹窗会显示以下信息：
- 标题: "你想要修改 'require' 的路径吗？"
- 来源: "Lua"
- 详情: 显示检测到的重命名文件数量
- 操作按钮: "修改" 和 "取消"

### 用户交互流程

1. 用户在VSCode中重命名Lua文件或目录
2. 语言服务器检测到重命名事件
3. 收集所有相关的Lua文件重命名信息
4. 弹出确认对话框
5. 用户选择"修改"或"取消"
6. 根据用户选择执行相应的操作

## 技术实现

### LSP协议支持

使用LSP的`window/showMessageRequest`方法实现用户确认功能：

```rust
pub async fn show_message_request(
    &self,
    params: ShowMessageRequestParams,
    cancel_token: CancellationToken,
) -> Option<MessageActionItem>
```

### 消息格式

```rust
ShowMessageRequestParams {
    typ: MessageType::INFO,
    message: "你想要修改 'require' 的路径吗？\n\n来源: Lua\n\n检测到 N 个文件被重命名",
    actions: Some(vec![
        MessageActionItem { title: "修改", properties: HashMap::new() },
        MessageActionItem { title: "取消", properties: HashMap::new() },
    ]),
}
```

## 配置选项

该功能会自动识别以下文件类型：
- `.lua` 文件（默认）
- 用户在客户端配置中指定的其他扩展名

## 未来扩展

- [ ] 实现具体的require路径修改逻辑
- [ ] 支持更多的文件引用模式
- [ ] 添加配置选项来控制是否显示确认对话框
- [ ] 支持批量操作的撤销功能
