name: Upload Crates

on:
  workflow_call:
  push:
    tags:
      - "*"

jobs:
  upload:
    runs-on: ubuntu-latest
    steps:
      - name: Clone repo
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable

      - name: Publish
        run: |
          for dir in emmylua_{code_analysis,ls,doc_cli,check}; do
            pushd "crates/$dir"
            cargo publish --allow-dirty
            popd
          done
        env:
          CARGO_REGISTRY_TOKEN: ${{ secrets.CARGO_TOKEN }}
