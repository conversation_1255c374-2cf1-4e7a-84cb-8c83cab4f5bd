[package]
name = "emmylua_doc_cli"
version = "0.8.1"
edition = "2021"
authors = ["CppCXY"]
description = "A command-line tool for generating lua documentation."
license = "MIT"
repository = "https://github.com/CppCXY/emmylua-analyzer-rust"
readme = "README.md"
keywords = ["emmylua", "doc", "lua", "cli"]
categories = ["development-tools"]
include = [
    "Cargo.toml",
    "src/**",
    "template/**",
]

[dependencies]
# local
emmylua_code_analysis.workspace = true
emmylua_parser.workspace = true

# external
serde.workspace = true
serde_json.workspace = true
lsp-types.workspace = true
rowan.workspace = true
walkdir.workspace = true
tera.workspace = true
include_dir.workspace = true
clap.workspace = true
