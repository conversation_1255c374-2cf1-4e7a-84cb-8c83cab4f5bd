use rowan::TextRange;

use crate::text::Source<PERSON><PERSON><PERSON>;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ial<PERSON>q, <PERSON>q, <PERSON>h)]
pub enum LuaParseErrorKind {
    SyntaxError,
    DocError,
}

#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub struct LuaParseError {
    pub kind: LuaParseErrorKind,
    pub message: String,
    pub range: TextRange,
}

impl LuaParseError {
    pub fn new(kind: LuaParseErrorKind, message: &str, range: TextRange) -> Self {
        LuaParseError {
            kind,
            message: message.to_string(),
            range,
        }
    }

    pub fn syntax_error_from(message: &str, range: SourceRange) -> Self {
        LuaParseError {
            kind: LuaParseErrorKind::SyntaxError,
            message: message.to_string(),
            range: range.into(),
        }
    }

    pub fn doc_error_from(message: &str, range: SourceRange) -> Self {
        LuaParseError {
            kind: LuaParseErrorKind::DocError,
            message: message.to_string(),
            range: range.into(),
        }
    }
}
