[package]
name = "emmylua_diagnostic_macro"
version = "0.4.0"
edition = "2021"
authors = ["CppCXY"]
description = "A proc-macro for generating diagnostic code for emmylua-analyzer-rust"
license = "MIT"
repository = "https://github.com/CppCXY/emmylua-analyzer-rust"
readme = "README.md"
keywords = ["emmylua", "lua"]
categories = ["development-tools"]

[lib]
proc-macro = true

[dependencies]
proc-macro2.workspace = true
syn.workspace = true
quote.workspace = true
