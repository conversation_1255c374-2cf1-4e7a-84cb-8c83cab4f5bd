use emmylua_parser::{<PERSON><PERSON><PERSON><PERSON>, LuaAstNode};
use rowan::NodeOrToken;

#[allow(unused)]
#[derive(Debug)]
pub struct LuaFormatter {
    root: Lua<PERSON>t,
}

#[allow(unused)]
impl LuaFormatter {
    pub fn new(root: <PERSON><PERSON><PERSON>t) -> Self {
        Self { root }
    }

    pub fn get_formatted_text(&self) -> String {
        let mut formatted_text = String::new();
        for node_or_token in self.root.syntax().descendants_with_tokens() {
            if let NodeOrToken::Token(token) = node_or_token {
                formatted_text.push_str(&token.text());
            }
        }

        formatted_text
    }
}
