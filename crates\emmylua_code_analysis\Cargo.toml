[package]
name = "emmylua_code_analysis"
version = "0.8.1"
edition = "2021"
authors = ["CppCXY"]
description = "A library for analyzing lua code."
license = "MIT"
repository = "https://github.com/CppCXY/emmylua-analyzer-rust"
readme = "README.md"
keywords = ["emmylua", "doc", "lua", "code_analysis"]
categories = ["development-tools"]
include = [
    "Cargo.toml",
    "src/**",
    "resources/**",
]

[dependencies]
# local
emmylua_parser.workspace = true
emmylua_diagnostic_macro.workspace = true

# external
serde.workspace = true
serde_json.workspace = true
lsp-types.workspace = true
schemars.workspace = true
rowan.workspace = true
regex.workspace = true
internment.workspace = true
log.workspace = true
tokio-util.workspace = true
rust-i18n.workspace = true
walkdir.workspace = true
dirs.workspace = true
wax.workspace = true
percent-encoding.workspace = true
flagset.workspace = true
encoding_rs.workspace = true
url.workspace = true
smol_str.workspace = true
serde_with.workspace = true
include_dir.workspace = true
emmylua_codestyle.workspace = true
itertools.workspace = true

[package.metadata.i18n]
available-locales = ["en", "zh_CN", "zh_HK"]
default-locale = "en"
