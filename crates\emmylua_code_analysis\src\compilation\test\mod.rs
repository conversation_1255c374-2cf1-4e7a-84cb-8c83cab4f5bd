mod and_or_test;
mod annotation_test;
mod closure_generic;
mod closure_param_infer_test;
mod closure_return_test;
mod decl_test;
mod diagnostic_disable_test;
mod flow;
mod for_range_var_infer_test;
mod infer_str_tpl_test;
mod inherit_type;
mod mathlib_test;
mod member_infer_test;
mod metatable_test;
mod module_annotation;
mod multi_return;
mod out_of_order;
mod overload_field;
mod overload_test;
mod pcall_test;
mod return_unwrap_test;
mod static_cal_cmp;
mod syntax_error_test;
mod tuple_test;
mod type_check_test;
mod unpack_test;
