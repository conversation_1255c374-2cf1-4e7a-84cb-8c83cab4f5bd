[package]
name = "emmylua_ls"
version = "0.8.1"
edition = "2021"
authors = ["CppCXY"]
description = "A language server for emmylua."
license = "MIT"
repository = "https://github.com/CppCXY/emmylua-analyzer-rust"
readme = "README.md"
keywords = ["emmylua", "doc", "lua"]
categories = ["development-tools"]

[dependencies]
# local
emmylua_code_analysis.workspace = true
emmylua_parser.workspace = true

# external
lsp-server.workspace = true
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
lsp-types.workspace = true
log.workspace = true
fern.workspace = true
chrono.workspace = true
notify.workspace = true
tokio-util.workspace = true
rowan.workspace = true
walkdir.workspace = true
rust-i18n.workspace = true
glob.workspace = true
serde_yml.workspace = true
itertools.workspace = true
dirs.workspace = true
clap.workspace = true
wax.workspace = true
